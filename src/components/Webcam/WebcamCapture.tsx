import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from "react";
import { Button } from "../ui/button";
import { useToast } from "../../contexts/toast";

export interface WebcamDevice {
  deviceId: string;
  label: string;
}

export interface CapturedImage {
  path: string;
  preview: string;
}

interface WebcamCaptureProps {
  onImageCaptured?: (image: CapturedImage) => void;
}

export interface WebcamCaptureRef {
  captureImage: () => Promise<void>;
  isReady: () => boolean;
}

export const WebcamCapture = forwardRef<WebcamCaptureRef, WebcamCaptureProps>(({ onImageCaptured }, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [devices, setDevices] = useState<WebcamDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [capturedImage, setCapturedImage] = useState<CapturedImage | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { showToast } = useToast();

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    captureImage: async () => {
      await captureImage();
    },
    isReady: () => {
      // Improved readiness detection - check video is actually playing
      const ready = !isLoading && !!stream && videoRef.current !== null && videoRef.current.readyState >= 2;
      console.log("WebcamCapture isReady check:", {
        isLoading,
        hasStream: !!stream,
        hasVideoRef: videoRef.current !== null,
        videoReadyState: videoRef.current?.readyState,
        finalReady: ready
      });
      return ready;
    }
  }));

  // Load available webcams on component mount
  useEffect(() => {
    loadWebcams();

    // Cleanup function to stop the stream when component unmounts
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  // Effect to handle visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !stream && selectedDevice) {
        console.log('Page became visible, restarting webcam');
        startWebcam(selectedDevice);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Enhanced periodic check to ensure webcam is working properly
    const intervalId = setInterval(() => {
      // Check if stream exists but video isn't playing (readyState < 2 means not enough data)
      if (videoRef.current && (
          (videoRef.current.srcObject === null && selectedDevice && !capturedImage) ||
          (stream && videoRef.current.readyState < 2 && !capturedImage)
        )) {
        console.log('Webcam stream lost or not playing properly, attempting to restart');
        startWebcam(selectedDevice);
      }
    }, 3000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(intervalId);
    };
  }, [stream, selectedDevice, capturedImage]);

  const loadWebcams = async () => {
    try {
      console.log('Loading webcams...');
      setIsLoading(true);
      setError(null);
      setCapturedImage(null); // Reset any captured image to start fresh

      // First, request camera permission to ensure labels are populated
      try {
        console.log('Requesting initial camera permission...');
        const initialStream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 }
          }
        });

        // Stop this stream immediately, we just needed it for permissions
        initialStream.getTracks().forEach(track => track.stop());
        console.log('Initial permission granted, stopped temporary stream');
      } catch (permissionError) {
        console.error('Error getting initial camera permission:', permissionError);
        setError("Camera permission denied. Please allow camera access and try again.");
        throw permissionError; // Throw to be caught by outer try-catch
      }

      // Get list of video input devices
      console.log('Enumerating devices...');
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');

      console.log(`Found ${videoDevices.length} video devices:`, videoDevices);

      if (videoDevices.length > 0) {
        const formattedDevices = videoDevices.map((device, index) => ({
          deviceId: device.deviceId,
          label: device.label || `Camera ${index + 1}`
        }));

        console.log('Formatted devices:', formattedDevices);
        setDevices(formattedDevices);

        // Select the first device by default
        const defaultDevice = formattedDevices[0].deviceId;
        console.log(`Selecting default device: ${defaultDevice}`);
        setSelectedDevice(defaultDevice);

        // Start the webcam with the first device
        await startWebcam(defaultDevice);
      } else {
        console.warn('No video devices found');

        // Try with default device as fallback
        try {
          console.log('Trying with default device...');
          const defaultStream = await navigator.mediaDevices.getUserMedia({ video: true });

          if (videoRef.current) {
            videoRef.current.srcObject = defaultStream;
          }

          setStream(defaultStream);

          // Add a default device to the list
          const defaultDevice = {
            deviceId: '',
            label: 'Default Camera'
          };

          setDevices([defaultDevice]);
          setSelectedDevice('');
          console.log('Using default camera');
        } catch (defaultError) {
          console.error('Error using default camera:', defaultError);
          setError("No webcams detected or permission denied");
          showToast("Error", "No webcams detected", "error");
        }
      }
    } catch (error) {
      console.error("Error loading webcams:", error);
      setError("Failed to access webcams. Please ensure you've granted camera permissions.");
      showToast("Error", "Failed to access webcams", "error");

      // Add a retry button by setting devices to empty array
      setDevices([]);
    } finally {
      setIsLoading(false);
    }
  };

  const startWebcam = async (deviceId: string) => {
    try {
      console.log(`Starting webcam with device ID: ${deviceId}`);
      setIsLoading(true);
      setError(null); // Clear any previous errors

      // Stop any existing stream
      if (stream) {
        console.log('Stopping existing stream');
        stream.getTracks().forEach(track => {
          track.stop();
          console.log(`Stopped track: ${track.kind}`);
        });
        setStream(null);
      }

      // Clear any existing video source
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject = null;
      }

      // Small delay to ensure previous stream is fully stopped
      await new Promise(resolve => setTimeout(resolve, 300));

      console.log('Requesting webcam access...');

      // First try with exact deviceId
      let newStream;
      try {
        // Request access to the webcam with more conservative constraints
        const constraints = {
          video: {
            deviceId: deviceId ? { exact: deviceId } : undefined,
            width: { ideal: 640 },  // Lower resolution for better compatibility
            height: { ideal: 480 }, // Lower resolution for better compatibility
            frameRate: { max: 30 }  // Limit frame rate
          }
        };

        console.log('Trying with exact constraints:', JSON.stringify(constraints));
        newStream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (exactDeviceError) {
        console.warn('Failed with exact deviceId, trying fallback:', exactDeviceError);

        // Fallback to any available camera if specific device fails
        const fallbackConstraints = {
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            frameRate: { max: 30 }
          }
        };

        console.log('Fallback constraints:', JSON.stringify(fallbackConstraints));
        newStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
      }

      console.log('Got stream with tracks:', newStream.getTracks().map(t => t.kind).join(', '));

      // Set the stream as the source
      if (videoRef.current) {
        console.log('Setting video source');
        videoRef.current.srcObject = newStream;

        // Create a promise to ensure video starts playing
        const videoPlayPromise = new Promise((resolve, reject) => {
          // Set up event listeners for success and failure
          const timeoutId = setTimeout(() => {
            reject(new Error('Video load timeout'));
          }, 5000); // 5 second timeout

          videoRef.current!.onloadeddata = () => {
            clearTimeout(timeoutId);
            console.log('Video data loaded');
            resolve(true);
          };

          videoRef.current!.onerror = (e) => {
            clearTimeout(timeoutId);
            console.error('Video load error:', e);
            reject(new Error('Video load error'));
          };
        });

        try {
          // Wait for video to start playing
          await videoPlayPromise;
          await videoRef.current.play();
          console.log('Video playback started successfully');
        } catch (playError) {
          console.error('Error starting video playback:', playError);
          setError("Error starting webcam stream. Please try again.");
          throw playError; // Re-throw to be caught by outer try-catch
        }
      } else {
        console.error('Video element not found');
        setError("Video element not found. Please refresh the page.");
        return;
      }

      // Update state
      setStream(newStream);
      setError(null);
      console.log('Webcam started successfully');
    } catch (error) {
      console.error("Error starting webcam:", error);
      setError("Failed to start webcam. Please check permissions and try again.");
      showToast("Error", "Failed to start webcam", "error");

      // Try with default device as fallback
      if (deviceId !== '') {
        console.log('Trying with default device as fallback');
        try {
          const defaultStream = await navigator.mediaDevices.getUserMedia({
            video: true
          });

          if (videoRef.current) {
            videoRef.current.srcObject = defaultStream;
          }

          setStream(defaultStream);
          setError(null);
          console.log('Webcam started with default device');
        } catch (fallbackError) {
          console.error("Fallback also failed:", fallbackError);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeviceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const deviceId = e.target.value;
    setSelectedDevice(deviceId);
    startWebcam(deviceId);
  };

  const captureImage = async () => {
    if (!videoRef.current || !canvasRef.current || !stream) {
      showToast("Error", "Webcam not ready", "error");
      return;
    }

    try {
      setIsLoading(true);

      const video = videoRef.current;
      const canvas = canvasRef.current;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the current video frame to the canvas
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error("Could not get canvas context");
      }

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert the canvas to a data URL (base64 encoded image)
      // Use PNG format with high quality
      const dataUrl = canvas.toDataURL('image/png', 1.0);

      console.log("Saving webcam image...");

      // Save the image via Electron
      const result = await window.electronAPI.saveWebcamImage(dataUrl);

      if (result.success && result.path) {
        console.log("Webcam image saved successfully:", result.path);

        // Create the image object with path and preview
        const image = {
          path: result.path,
          preview: dataUrl
        };

        // Update state
        setCapturedImage(image);

        // Notify parent component
        if (onImageCaptured) {
          onImageCaptured(image);
        }

        showToast("Success", "Image captured successfully", "success");
      } else {
        console.error("Failed to save webcam image:", result.error);
        showToast("Error", result.error || "Failed to save image", "error");
      }
    } catch (error) {
      console.error("Error capturing image:", error);
      showToast("Error", "Failed to capture image", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const deleteImage = async () => {
    if (!capturedImage) return;

    try {
      setIsLoading(true);
      const result = await window.electronAPI.deleteWebcamImage(capturedImage.path);

      if (result.success) {
        setCapturedImage(null);
        showToast("Success", "Image deleted successfully", "success");
      } else {
        showToast("Error", result.error || "Failed to delete image", "error");
      }
    } catch (error) {
      console.error("Error deleting image:", error);
      showToast("Error", "Failed to delete image", "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4 p-4 bg-black/30 rounded-lg border border-white/10">
      <h2 className="text-lg font-medium text-white">Webcam Capture</h2>

      {/* Webcam selection */}
      <div className="space-y-2">
        <label className="text-sm text-white/70">Select Webcam</label>
        <select
          value={selectedDevice}
          onChange={handleDeviceChange}
          disabled={isLoading || devices.length === 0}
          className="w-full bg-black/50 text-white rounded-md border border-white/20 p-2"
        >
          {devices.length === 0 ? (
            <option value="">No webcams available</option>
          ) : (
            devices.map((device) => (
              <option key={device.deviceId} value={device.deviceId}>
                {device.label}
              </option>
            ))
          )}
        </select>
      </div>

      {/* Live webcam preview */}
      <div className="relative bg-black rounded-md overflow-hidden" style={{ minHeight: "240px" }}>
        {!capturedImage && (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-auto rounded-md"
            style={{ display: capturedImage ? 'none' : 'block', minHeight: "240px" }}
          />
        )}

        {/* Hidden canvas for capturing images */}
        <canvas
          ref={canvasRef}
          className="hidden"
        />

        {/* Loading indicator */}
        {isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-white"></div>
              <p className="mt-2">Loading webcam...</p>
            </div>
          </div>
        )}

        {/* Error message */}
        {error && !stream && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80 text-white p-4 text-center">
            <div>
              <p className="mb-2">{error}</p>
              <Button
                onClick={() => {
                  setError(null);
                  loadWebcams();
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm"
              >
                Try Again
              </Button>
            </div>
          </div>
        )}

        {/* No stream message */}
        {!stream && !error && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80 text-white p-4 text-center">
            <div>
              <p className="mb-2">Webcam not active. Click below to start.</p>
              <Button
                onClick={() => {
                  if (selectedDevice) {
                    startWebcam(selectedDevice);
                  } else {
                    loadWebcams();
                  }
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm"
              >
                Start Webcam
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Capture button is now controlled from parent via ref */}

      {/* Preview captured image - styled like screenshots */}
      {capturedImage && (
        <div className="mt-4">
          <div className="bg-black/30 rounded-lg border border-white/10 p-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-white">Captured Image</h3>
              <div className="flex gap-2">
                <button
                  onClick={() => setCapturedImage(null)}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded-md text-xs px-2"
                  title="Return to camera"
                >
                  Back to Camera
                </button>
                <button
                  onClick={deleteImage}
                  className="bg-red-600 hover:bg-red-700 text-white p-1 rounded-md text-xs px-2"
                  title="Delete image"
                >
                  Delete
                </button>
              </div>
            </div>

            <div className="relative rounded-md overflow-hidden border border-white/20">
              <img
                src={capturedImage.preview}
                alt="Captured"
                className="w-full h-auto"
              />
            </div>

            <div className="mt-2 flex justify-between items-center">
              <span className="text-xs text-white/60">
                {new Date().toLocaleTimeString()} • Webcam Capture
              </span>
              <span className="text-xs text-green-400">
                Ready for processing
              </span>
            </div>
          </div>
        </div>
      )}

      {/* No webcams message */}
      {devices.length === 0 && !isLoading && (
        <div className="text-center text-white/60 py-4">
          No webcams detected. Please connect a webcam and refresh.
          <Button
            onClick={loadWebcams}
            className="mt-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md"
          >
            Refresh
          </Button>
        </div>
      )}
    </div>
  );
});

WebcamCapture.displayName = 'WebcamCapture';

export default WebcamCapture;
