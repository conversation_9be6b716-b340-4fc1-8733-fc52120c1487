<!DOCTYPE html>
<html>
<head>
    <title>Camera Test</title>
</head>
<body>
    <h1>Camera Test</h1>
    <video id="video" width="640" height="480" autoplay></video>
    <br>
    <button onclick="startCamera()">Start Camera</button>
    <button onclick="stopCamera()">Stop Camera</button>
    <div id="status"></div>

    <script>
        let stream = null;
        const video = document.getElementById('video');
        const status = document.getElementById('status');

        async function startCamera() {
            try {
                status.textContent = 'Requesting camera access...';
                stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
                status.textContent = 'Camera started successfully!';
                console.log('Camera stream:', stream);
                console.log('Video readyState:', video.readyState);
            } catch (error) {
                status.textContent = 'Error: ' + error.message;
                console.error('Camera error:', error);
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                video.srcObject = null;
                stream = null;
                status.textContent = 'Camera stopped';
            }
        }

        // Auto-start camera
        startCamera();
    </script>
</body>
</html>
