// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, BrowserWindow, dialog } from "electron"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk';

// Interface for Gemini API requests
interface GeminiMessage {
  role: string;
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    }
  }>;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
}
interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    source?: {
      type: 'base64';
      media_type: string;
      data: string;
    };
  }>;
}
export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()

    // Initialize AI client based on config
    this.initializeAIClient();

    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient();
    });
  }

  /**
   * Initialize or reinitialize all AI clients with environment variables
   */
  private initializeAIClient(): void {
    try {
      const allKeys = configHelper.getAllApiKeys();

      // Initialize OpenAI client if key is available
      if (allKeys.openai) {
        this.openaiClient = new OpenAI({
          apiKey: allKeys.openai,
          timeout: 60000, // 60 second timeout
          maxRetries: 2   // Retry up to 2 times
        });
        console.log("OpenAI client initialized successfully");
      } else {
        this.openaiClient = null;
        console.warn("No OpenAI API key available");
      }

      // Initialize Gemini API key if available
      if (allKeys.gemini) {
        this.geminiApiKey = allKeys.gemini;
        console.log("Gemini API key set successfully");
      } else {
        this.geminiApiKey = null;
        console.warn("No Gemini API key available");
      }

      // Initialize Anthropic client if key is available
      if (allKeys.anthropic) {
        this.anthropicClient = new Anthropic({
          apiKey: allKeys.anthropic,
          timeout: 60000, // 60 second timeout
          maxRetries: 2   // Retry up to 2 times
        });
        console.log("Anthropic client initialized successfully");
      } else {
        this.anthropicClient = null;
        console.warn("No Anthropic API key available");
      }

      // Log which providers are available
      const availableProviders = [];
      if (this.openaiClient) availableProviders.push("OpenAI");
      if (this.geminiApiKey) availableProviders.push("Gemini");
      if (this.anthropicClient) availableProviders.push("Anthropic");

      console.log(`Initialized AI clients for: ${availableProviders.join(", ")}`);
    } catch (error) {
      console.error("Error initializing AI clients:", error);
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
    }
  }

  /**
   * Get available API providers based on configured API keys
   */
  private getAvailableProviders(): Array<"openai" | "gemini" | "anthropic"> {
    const config = configHelper.loadConfig();
    const providers: Array<"openai" | "gemini" | "anthropic"> = [];

    // For now, only return the primary configured provider
    // since we only support one API key at a time
    if (config.apiKey && config.apiProvider) {
      providers.push(config.apiProvider);
    }

    return providers;
  }

  /**
   * Try processing with fallback to other providers
   */
  private async tryWithFallback<T>(
    operation: (provider: "openai" | "gemini" | "anthropic") => Promise<T>,
    operationName: string
  ): Promise<T> {
    const config = configHelper.loadConfig();
    const primaryProvider = config.apiProvider;

    try {
      console.log(`Attempting ${operationName} with ${primaryProvider} provider`);
      const result = await operation(primaryProvider);
      return result;
    } catch (error: any) {
      console.error(`${operationName} failed with ${primaryProvider} provider:`, error);

      // Don't retry if it's a cancellation
      if (axios.isCancel(error)) {
        throw error;
      }

      // Provide helpful error message suggesting alternative providers
      let errorMessage = error.message || `Failed to ${operationName}`;

      if (error.message && error.message.includes('JSON')) {
        errorMessage = `${primaryProvider} returned an invalid response format. Try switching to a different AI provider in settings (OpenAI, Gemini, or Anthropic) for better results.`;
      } else if (error.status === 429) {
        errorMessage = `${primaryProvider} API rate limit exceeded. Try switching to a different AI provider in settings or wait a few minutes before trying again.`;
      } else if (error.status === 401) {
        errorMessage = `${primaryProvider} API key is invalid. Please check your API key in settings or try a different AI provider.`;
      } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
        errorMessage = `Your screenshots contain too much information for ${primaryProvider} to process. Try switching to OpenAI or Gemini in settings which can handle larger inputs.`;
      } else {
        errorMessage = `${primaryProvider} failed to ${operationName}. Try switching to a different AI provider in settings (OpenAI, Gemini, or Anthropic) for better results.`;
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Process with all three providers simultaneously for comparison
   */
  private async processWithAllProviders<T>(
    operation: (provider: "openai" | "gemini" | "anthropic") => Promise<T>,
    operationName: string
  ): Promise<{ openai?: T; gemini?: T; anthropic?: T; errors?: { openai?: string; gemini?: string; anthropic?: string } }> {
    const results: { openai?: T; gemini?: T; anthropic?: T; errors?: { openai?: string; gemini?: string; anthropic?: string } } = {};

    // Initialize all clients if needed
    this.initializeAIClient();

    // Check which providers are available
    const allKeys = configHelper.getAllApiKeys();
    const hasOpenAI = !!allKeys.openai && !!this.openaiClient;
    const hasGemini = !!allKeys.gemini && !!this.geminiApiKey;
    const hasAnthropic = !!allKeys.anthropic && !!this.anthropicClient;

    if (!hasOpenAI && !hasGemini && !hasAnthropic) {
      throw new Error("No API keys configured. Please configure at least one API key in the .env file.");
    }

    // Run all available providers in parallel
    const promises: Promise<void>[] = [];

    if (hasOpenAI) {
      promises.push(
        operation("openai")
          .then(result => {
            results.openai = result;
            console.log(`${operationName} succeeded with OpenAI`);
          })
          .catch(error => {
            console.error(`${operationName} failed with OpenAI:`, error);
            if (!results.errors) results.errors = {};
            results.errors.openai = error.message || `OpenAI failed to ${operationName}`;
          })
      );
    }

    if (hasGemini) {
      promises.push(
        operation("gemini")
          .then(result => {
            results.gemini = result;
            console.log(`${operationName} succeeded with Gemini`);
          })
          .catch(error => {
            console.error(`${operationName} failed with Gemini:`, error);
            if (!results.errors) results.errors = {};
            results.errors.gemini = error.message || `Gemini failed to ${operationName}`;
          })
      );
    }

    if (hasAnthropic) {
      promises.push(
        operation("anthropic")
          .then(result => {
            results.anthropic = result;
            console.log(`${operationName} succeeded with Anthropic`);
          })
          .catch(error => {
            console.error(`${operationName} failed with Anthropic:`, error);
            if (!results.errors) results.errors = {};
            results.errors.anthropic = error.message || `Anthropic failed to ${operationName}`;
          })
      );
    }

    // Wait for all promises to complete
    await Promise.all(promises);

    // Check if at least one succeeded
    if (!results.openai && !results.gemini && !results.anthropic) {
      const errorMessages = [];
      if (results.errors?.openai) errorMessages.push(`OpenAI: ${results.errors.openai}`);
      if (results.errors?.gemini) errorMessages.push(`Gemini: ${results.errors.gemini}`);
      if (results.errors?.anthropic) errorMessages.push(`Anthropic: ${results.errors.anthropic}`);
      throw new Error(`All providers failed for ${operationName}. ${errorMessages.join('; ')}`);
    }

    return results;
  }

  /**
   * Process with both Claude and OpenAI simultaneously for comparison (legacy method)
   */
  private async processWithDualProviders<T>(
    operation: (provider: "openai" | "anthropic") => Promise<T>,
    operationName: string
  ): Promise<{ openai?: T; anthropic?: T; errors?: { openai?: string; anthropic?: string } }> {
    // Use the new all providers method but only return OpenAI and Anthropic results
    const allResults = await this.processWithAllProviders(operation, operationName);
    return {
      openai: allResults.openai,
      anthropic: allResults.anthropic,
      errors: {
        openai: allResults.errors?.openai,
        anthropic: allResults.errors?.anthropic
      }
    };
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getCredits(): Promise<number> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return 999 // Unlimited credits in this version

    try {
      await this.waitForInitialization(mainWindow)
      return 999 // Always return sufficient credits to work
    } catch (error) {
      console.error("Error getting credits:", error)
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig();
      if (config.language) {
        return config.language;
      }

      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language;
          }
        } catch (err) {
          console.warn("Could not get language from window", err);
        }
      }

      // Default fallback
      return "python";
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();

    // First verify we have a valid AI client
    if (config.apiProvider === "openai" && !this.openaiClient) {
      this.initializeAIClient();

      if (!this.openaiClient) {
        console.error("OpenAI client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "gemini" && !this.geminiApiKey) {
      this.initializeAIClient();

      if (!this.geminiApiKey) {
        console.error("Gemini API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "anthropic" && !this.anthropicClient) {
      // Add check for Anthropic client
      this.initializeAIClient();

      if (!this.anthropicClient) {
        console.error("Anthropic client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    }

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)

      // Check if the queue is empty
      if (!screenshotQueue || screenshotQueue.length === 0) {
        console.log("No screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter(path => fs.existsSync(path));
      if (existingScreenshots.length === 0) {
        console.log("Screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map(async (path) => {
            try {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);

        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data");
        }

        const result = await this.processScreenshotsHelper(validScreenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key") || result.error?.includes("OpenAI") || result.error?.includes("Gemini")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)

      // Check if the extra queue is empty
      if (!extraScreenshotQueue || extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);

        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter(path => fs.existsSync(path));
      if (existingExtraScreenshots.length === 0) {
        console.log("Extra screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];

        const screenshots = await Promise.all(
          allPaths.map(async (path) => {
            try {
              if (!fs.existsSync(path)) {
                console.warn(`Screenshot file does not exist: ${path}`);
                return null;
              }

              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);

        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data for debugging");
        }

        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  /**
   * Extract problem information using a specific provider
   */
  private async extractProblemInfo(
    imageDataList: string[],
    language: string,
    provider: "openai" | "gemini" | "anthropic",
    signal: AbortSignal,
    isWebcamImage: boolean = false
  ): Promise<any> {
    const config = configHelper.loadConfig();

    if (provider === "openai") {
      // Verify OpenAI client
      if (!this.openaiClient) {
        this.initializeAIClient(); // Try to reinitialize

        if (!this.openaiClient) {
          throw new Error("OpenAI API key not configured or invalid. Please check your settings.");
        }
      }

      // Create different prompts based on whether it's a webcam image
      let systemPrompt, userPrompt;

      if (isWebcamImage) {
        systemPrompt = "You are an expert assistant that analyzes images to identify and answer questions. Analyze the image and determine the question type: multiple choice, short answer, code output, or coding problem. Return the information in JSON format with these fields: is_coding_problem (boolean), problem_statement, question_type (string: 'multiple_choice', 'short_answer', 'code_output', 'coding_problem', 'general'), is_multiple_choice (boolean), choices (array), correct_answer (string), explanation (string), code_snippet (string for code output questions). For coding problems, also include constraints, example_input, example_output. Just return the structured JSON without any other text.";
        userPrompt = `Analyze this webcam image and determine what type of question or problem it contains:

1. MULTIPLE CHOICE: Questions with options A, B, C, D, etc. - identify the correct answer
2. SHORT ANSWER: Questions requiring brief text responses - provide the answer
3. CODE OUTPUT: Questions showing code and asking "what does this output?" - analyze the code and provide the output
4. CODING PROBLEM: Programming challenges requiring code solutions
5. GENERAL: Other types of questions

Return in JSON format with appropriate fields:
- question_type: one of the types above
- For multiple choice: include is_multiple_choice: true, choices array, correct_answer, explanation
- For short answer: include correct_answer and explanation
- For code output: include code_snippet (the code shown), correct_answer (the output), explanation
- For coding problems: include constraints, example_input, example_output

Preferred coding language is ${language}.`;
      } else {
        systemPrompt = "You are a problem interpreter. Analyze the screenshot and determine if it contains a coding problem or a general question/problem. Return the information in JSON format with these fields: is_coding_problem (boolean), problem_statement, constraints, example_input, example_output. For non-coding problems, set is_coding_problem to false and include only the problem_statement field with the question or problem description. Just return the structured JSON without any other text.";
        userPrompt = `Analyze these screenshots and determine if they contain a coding problem or a general question/problem. Return in JSON format with is_coding_problem field (boolean). For non-coding problems, only include the problem_statement field. Preferred coding language we gonna use for this problem is ${language}.`;
      }

      // Use OpenAI for processing
      const messages = [
        {
          role: "system" as const,
          content: systemPrompt
        },
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: userPrompt
            },
            ...imageDataList.map(data => ({
              type: "image_url" as const,
              image_url: { url: `data:image/png;base64,${data}` }
            }))
          ]
        }
      ];

      // Send to OpenAI Vision API
      const extractionResponse = await this.openaiClient.chat.completions.create({
        model: config.extractionModel || "gpt-4o",
        messages: messages,
        max_tokens: 4000,
        temperature: 0.2
      });

      // Parse the response
      const responseText = extractionResponse.choices[0].message.content;
      // Handle when OpenAI might wrap the JSON in markdown code blocks
      const jsonText = responseText.replace(/```json|```/g, '').trim();
      return JSON.parse(jsonText);

    } else if (provider === "gemini") {
      // Use Gemini API
      if (!this.geminiApiKey) {
        throw new Error("Gemini API key not configured. Please check your settings.");
      }

      // Create different prompts for Gemini based on whether it's a webcam image
      let geminiPrompt;

      if (isWebcamImage) {
        geminiPrompt = `You are an expert assistant that analyzes images to identify and answer questions. Analyze the image and determine the question type: multiple choice, short answer, code output, or coding problem. Return the information in JSON format with these fields: is_coding_problem (boolean), problem_statement, question_type (string: 'multiple_choice', 'short_answer', 'code_output', 'coding_problem', 'general'), is_multiple_choice (boolean), choices (array), correct_answer (string), explanation (string), code_snippet (string for code output questions). For coding problems, also include constraints, example_input, example_output. Just return the structured JSON without any other text.

Analyze this webcam image and determine what type of question or problem it contains:

1. MULTIPLE CHOICE: Questions with options A, B, C, D, etc. - identify the correct answer
2. SHORT ANSWER: Questions requiring brief text responses - provide the answer
3. CODE OUTPUT: Questions showing code and asking "what does this output?" - analyze the code and provide the output
4. CODING PROBLEM: Programming challenges requiring code solutions
5. GENERAL: Other types of questions

Return in JSON format with appropriate fields:
- question_type: one of the types above
- For multiple choice: include is_multiple_choice: true, choices array, correct_answer, explanation
- For short answer: include correct_answer and explanation
- For code output: include code_snippet (the code shown), correct_answer (the output), explanation
- For coding problems: include constraints, example_input, example_output

Preferred coding language is ${language}.`;
      } else {
        geminiPrompt = `You are a problem interpreter. Analyze the screenshots and determine if they contain a coding problem or a general question/problem. Return the information in JSON format with these fields: is_coding_problem (boolean), problem_statement, constraints, example_input, example_output. For non-coding problems, set is_coding_problem to false and include only the problem_statement field with the question or problem description. Just return the structured JSON without any other text. Preferred coding language we gonna use for this problem is ${language}.`;
      }

      // Create Gemini message structure
      const geminiMessages: GeminiMessage[] = [
        {
          role: "user",
          parts: [
            {
              text: geminiPrompt
            },
            ...imageDataList.map(data => ({
              inlineData: {
                mimeType: "image/png",
                data: data
              }
            }))
          ]
        }
      ];

      // Make API request to Gemini
      const response = await axios.default.post(
        `https://generativelanguage.googleapis.com/v1beta/models/${config.extractionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
        {
          contents: geminiMessages,
          generationConfig: {
            temperature: 0.2,
            maxOutputTokens: 4000
          }
        },
        { signal }
      );

      const responseData = response.data as GeminiResponse;

      if (!responseData.candidates || responseData.candidates.length === 0) {
        throw new Error("Empty response from Gemini API");
      }

      const responseText = responseData.candidates[0].content.parts[0].text;

      // Handle when Gemini might wrap the JSON in markdown code blocks
      const jsonText = responseText.replace(/```json|```/g, '').trim();
      return JSON.parse(jsonText);

    } else if (provider === "anthropic") {
      if (!this.anthropicClient) {
        throw new Error("Anthropic API key not configured. Please check your settings.");
      }

      // Create different prompts for Anthropic based on whether it's a webcam image
      let anthropicPrompt;

      if (isWebcamImage) {
        anthropicPrompt = `You are an expert assistant that analyzes images to identify and answer questions. Analyze the image and determine the question type: multiple choice, short answer, code output, or coding problem. Return the information in JSON format with these fields: is_coding_problem (boolean), problem_statement, question_type (string: 'multiple_choice', 'short_answer', 'code_output', 'coding_problem', 'general'), is_multiple_choice (boolean), choices (array), correct_answer (string), explanation (string), code_snippet (string for code output questions). For coding problems, also include constraints, example_input, example_output. Just return the structured JSON without any other text.

Analyze this webcam image and determine what type of question or problem it contains:

1. MULTIPLE CHOICE: Questions with options A, B, C, D, etc. - identify the correct answer
2. SHORT ANSWER: Questions requiring brief text responses - provide the answer
3. CODE OUTPUT: Questions showing code and asking "what does this output?" - analyze the code and provide the output
4. CODING PROBLEM: Programming challenges requiring code solutions
5. GENERAL: Other types of questions

Return in JSON format with appropriate fields:
- question_type: one of the types above
- For multiple choice: include is_multiple_choice: true, choices array, correct_answer, explanation
- For short answer: include correct_answer and explanation
- For code output: include code_snippet (the code shown), correct_answer (the output), explanation
- For coding problems: include constraints, example_input, example_output

Preferred coding language is ${language}.`;
      } else {
        anthropicPrompt = `Analyze the screenshots and determine if they contain a coding problem or a general question/problem. Return the information in JSON format with these fields: is_coding_problem (boolean), problem_statement, constraints, example_input, example_output. For non-coding problems, set is_coding_problem to false and include only the problem_statement field with the question or problem description. Preferred coding language is ${language}.`;
      }

      const messages = [
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: anthropicPrompt
            },
            ...imageDataList.map(data => ({
              type: "image" as const,
              source: {
                type: "base64" as const,
                media_type: "image/png" as const,
                data: data
              }
            }))
          ]
        }
      ];

      const response = await this.anthropicClient.messages.create({
        model: config.extractionModel || "claude-3-7-sonnet-20250219",
        max_tokens: 4000,
        messages: messages,
        temperature: 0.2
      });

      const responseText = (response.content[0] as { type: 'text', text: string }).text;
      const jsonText = responseText.replace(/```json|```/g, '').trim();
      return JSON.parse(jsonText);
    }

    throw new Error(`Unsupported provider: ${provider}`);
  }

  /**
   * Get the appropriate model name for each provider
   */
  private getModelForProvider(provider: "openai" | "gemini" | "anthropic"): string {
    // Use hardcoded model names for each provider to avoid cross-contamination
    switch (provider) {
      case "openai":
        return "gpt-4o"; // Always use gpt-4o for OpenAI
      case "gemini":
        return "gemini-2.0-flash-exp"; // Use the correct Gemini model
      case "anthropic":
        return "claude-3-5-sonnet-20241022"; // Use the correct Claude model
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Generate solution using a specific provider
   */
  private async generateSolution(
    problemInfo: any,
    language: string,
    provider: "openai" | "gemini" | "anthropic",
    signal: AbortSignal
  ): Promise<string> {
    const config = configHelper.loadConfig();
    const modelName = this.getModelForProvider(provider);

    // Check if this is a webcam image (stored during extraction)
    const isWebcamImage = this.deps.getIsWebcamImage?.() || false;

    // For webcam images, provide direct, clean answers
    if (isWebcamImage) {
      return await this.generateWebcamAnswer(problemInfo, language, provider, signal);
    }

    // Check the question type and handle accordingly
    const isCodingProblem = problemInfo.is_coding_problem !== false; // Default to true if not specified
    const isMultipleChoice = problemInfo.is_multiple_choice === true;
    const questionType = problemInfo.question_type || 'general';

    // Create prompt for solution generation based on problem type
    let promptText;

    if (questionType === 'multiple_choice' || isMultipleChoice) {
      // For multiple choice questions, provide a direct answer format
      promptText = `
The following multiple choice question has been analyzed:

QUESTION:
${problemInfo.problem_statement}

CHOICES:
${problemInfo.choices ? problemInfo.choices.map((choice: string, index: number) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n') : 'Choices not clearly identified'}

CORRECT ANSWER: ${problemInfo.correct_answer || 'Not determined'}

EXPLANATION:
${problemInfo.explanation || 'No explanation provided'}

Please provide a comprehensive response in the following format:
1. Answer: The correct choice (letter and full text)
2. Your Thoughts: Detailed reasoning and explanation for why this is correct
3. Additional Context: Any relevant background information or concepts that help understand the answer
`;
    } else if (questionType === 'short_answer') {
      // For short answer questions
      promptText = `
The following short answer question has been analyzed:

QUESTION:
${problemInfo.problem_statement}

CORRECT ANSWER: ${problemInfo.correct_answer || 'Not determined'}

EXPLANATION:
${problemInfo.explanation || 'No explanation provided'}

Please provide a comprehensive response in the following format:
1. Answer: The correct short answer
2. Your Thoughts: Detailed reasoning and explanation for this answer
3. Additional Context: Any relevant background information or concepts that help understand the answer
`;
    } else if (questionType === 'code_output') {
      // For code output questions
      promptText = `
The following code output question has been analyzed:

QUESTION:
${problemInfo.problem_statement}

CODE SNIPPET:
${problemInfo.code_snippet || 'Code not clearly identified'}

CORRECT OUTPUT: ${problemInfo.correct_answer || 'Not determined'}

EXPLANATION:
${problemInfo.explanation || 'No explanation provided'}

Please provide a comprehensive response in the following format:
1. Answer: The exact output the code will produce
2. Your Thoughts: Step-by-step explanation of how the code executes to produce this output
3. Additional Context: Any relevant programming concepts or language features that affect the output
`;
    } else if (isCodingProblem || questionType === 'coding_problem') {
      promptText = `
Generate a detailed solution for the following coding problem:

PROBLEM STATEMENT:
${problemInfo.problem_statement}

CONSTRAINTS:
${problemInfo.constraints || "No specific constraints provided."}

EXAMPLE INPUT:
${problemInfo.example_input || "No example input provided."}

EXAMPLE OUTPUT:
${problemInfo.example_output || "No example output provided."}

LANGUAGE: ${language}

I need the response in the following format:
1. Code: A clean, optimized implementation in ${language}
2. Your Thoughts: A list of key insights and reasoning behind your approach
3. Time complexity: O(X) with a detailed explanation (at least 2 sentences)
4. Space complexity: O(X) with a detailed explanation (at least 2 sentences)

For complexity explanations, please be thorough. For example: "Time complexity: O(n) because we iterate through the array only once. This is optimal as we need to examine each element at least once to find the solution." or "Space complexity: O(n) because in the worst case, we store all elements in the hashmap. The additional space scales linearly with the input size."

Your solution should be efficient, well-commented, and handle edge cases.
`;
    } else {
      promptText = `
Answer the following question or problem:

QUESTION/PROBLEM:
${problemInfo.problem_statement}

Provide a comprehensive, accurate, and helpful answer. Include relevant examples, explanations, and context where appropriate. Format your answer in a clear and organized way.

I need the response in the following format:
1. Answer: A clear and comprehensive answer to the question
2. Your Thoughts: A list of key insights and reasoning behind your answer
`;
    }

    if (provider === "openai") {
      // OpenAI processing
      if (!this.openaiClient) {
        throw new Error("OpenAI API key not configured. Please check your settings.");
      }

      // Send to OpenAI API
      const solutionResponse = await this.openaiClient.chat.completions.create({
        model: modelName,
        messages: [
          { role: "system", content:
            questionType === 'multiple_choice' || isMultipleChoice ?
            "You are an expert assistant that provides clear, accurate answers to multiple choice questions with detailed explanations." :
            questionType === 'short_answer' ?
            "You are an expert assistant that provides clear, accurate short answers with detailed explanations." :
            questionType === 'code_output' ?
            "You are an expert programming assistant that analyzes code and predicts its exact output with detailed step-by-step explanations." :
            isCodingProblem || questionType === 'coding_problem' ?
            "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." :
            "You are a knowledgeable assistant. Provide comprehensive, accurate, and helpful answers to questions."
          },
          { role: "user", content: promptText }
        ],
        max_tokens: 4000,
        temperature: 0.2
      });

      return solutionResponse.choices[0].message.content || "";

    } else if (provider === "gemini") {
      // Gemini processing
      if (!this.geminiApiKey) {
        throw new Error("Gemini API key not configured. Please check your settings.");
      }

      // Create Gemini message structure
      const geminiMessages = [
        {
          role: "user",
          parts: [
            {
              text:
                questionType === 'multiple_choice' || isMultipleChoice ?
                `You are an expert assistant that provides clear, accurate answers to multiple choice questions with detailed explanations:\n\n${promptText}` :
                questionType === 'short_answer' ?
                `You are an expert assistant that provides clear, accurate short answers with detailed explanations:\n\n${promptText}` :
                questionType === 'code_output' ?
                `You are an expert programming assistant that analyzes code and predicts its exact output with detailed step-by-step explanations:\n\n${promptText}` :
                isCodingProblem || questionType === 'coding_problem' ?
                `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}` :
                `You are a knowledgeable assistant. Provide a comprehensive, accurate, and helpful answer to this question:\n\n${promptText}`
            }
          ]
        }
      ];

      // Make API request to Gemini
      const response = await axios.default.post(
        `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${this.geminiApiKey}`,
        {
          contents: geminiMessages,
          generationConfig: {
            temperature: 0.2,
            maxOutputTokens: 4000
          }
        },
        { signal }
      );

      const responseData = response.data as GeminiResponse;

      if (!responseData.candidates || responseData.candidates.length === 0) {
        throw new Error("Empty response from Gemini API");
      }

      return responseData.candidates[0].content.parts[0].text;

    } else if (provider === "anthropic") {
      // Anthropic processing
      if (!this.anthropicClient) {
        throw new Error("Anthropic API key not configured. Please check your settings.");
      }

      const messages = [
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text:
                questionType === 'multiple_choice' || isMultipleChoice ?
                `You are an expert assistant that provides clear, accurate answers to multiple choice questions with detailed explanations:\n\n${promptText}` :
                questionType === 'short_answer' ?
                `You are an expert assistant that provides clear, accurate short answers with detailed explanations:\n\n${promptText}` :
                questionType === 'code_output' ?
                `You are an expert programming assistant that analyzes code and predicts its exact output with detailed step-by-step explanations:\n\n${promptText}` :
                isCodingProblem || questionType === 'coding_problem' ?
                `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}` :
                `You are a knowledgeable assistant. Provide a comprehensive, accurate, and helpful answer to this question:\n\n${promptText}`
            }
          ]
        }
      ];

      // Send to Anthropic API
      const response = await this.anthropicClient.messages.create({
        model: modelName,
        max_tokens: 4000,
        messages: messages,
        temperature: 0.2
      });

      return (response.content[0] as { type: 'text', text: string }).text;
    }

    throw new Error(`Unsupported provider: ${provider}`);
  }

  /**
   * Generate direct, clean answers for webcam images
   */
  private async generateWebcamAnswer(
    problemInfo: any,
    language: string,
    provider: "openai" | "gemini" | "anthropic",
    signal: AbortSignal
  ): Promise<string> {
    const config = configHelper.loadConfig();
    const modelName = this.getModelForProvider(provider);
    const questionType = problemInfo.question_type || 'general';

    let prompt = '';

    switch (questionType) {
      case 'multiple_choice':
        prompt = `Question: ${problemInfo.problem_statement}

${problemInfo.choices ? `Choices:\n${problemInfo.choices.map((choice: string, index: number) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n')}` : ''}

Provide the correct answer and a brief explanation:

Answer: [Letter/Option]
Explanation: [Brief explanation why this is correct]`;
        break;

      case 'short_answer':
        prompt = `Question: ${problemInfo.problem_statement}

Provide a direct, concise answer to this question.`;
        break;

      case 'code_output':
        prompt = `Code: ${problemInfo.code_snippet || problemInfo.problem_statement}

Analyze this code and provide the exact output it would produce. Be precise and show the exact result.`;
        break;

      case 'coding_problem':
        prompt = `Problem: ${problemInfo.problem_statement}

${problemInfo.constraints ? `Constraints: ${problemInfo.constraints}` : ''}
${problemInfo.example_input ? `Example Input: ${problemInfo.example_input}` : ''}
${problemInfo.example_output ? `Example Output: ${problemInfo.example_output}` : ''}

Provide a clean, working solution in ${language}. Include brief comments explaining the approach.`;
        break;

      default:
        prompt = `Question/Problem: ${problemInfo.problem_statement}

Provide a clear, direct answer to this question or problem.`;
    }

    // Call the appropriate provider with simplified prompts
    if (provider === "openai") {
      if (!this.openaiClient) {
        throw new Error("OpenAI API key not configured. Please check your settings.");
      }

      const response = await this.openaiClient.chat.completions.create({
        model: modelName,
        messages: [
          { role: "system", content: "You are a helpful assistant that provides clear, direct answers. Be concise but accurate." },
          { role: "user", content: prompt }
        ],
        max_tokens: 2000,
        temperature: 0.1
      });

      return response.choices[0].message.content || "";

    } else if (provider === "gemini") {
      if (!this.geminiApiKey) {
        throw new Error("Gemini API key not configured. Please check your settings.");
      }

      const geminiMessages = [
        {
          role: "user",
          parts: [
            {
              text: `You are a helpful assistant that provides clear, direct answers. Be concise but accurate.\n\n${prompt}`
            }
          ]
        }
      ];

      const response = await axios.default.post(
        `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${this.geminiApiKey}`,
        {
          contents: geminiMessages,
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 2000
          }
        },
        { signal }
      );

      const responseData = response.data as GeminiResponse;

      if (!responseData.candidates || responseData.candidates.length === 0) {
        throw new Error("Empty response from Gemini API");
      }

      return responseData.candidates[0].content.parts[0].text;

    } else if (provider === "anthropic") {
      if (!this.anthropicClient) {
        throw new Error("Anthropic API key not configured. Please check your settings.");
      }

      const messages = [
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: `You are a helpful assistant that provides clear, direct answers. Be concise but accurate.\n\n${prompt}`
            }
          ]
        }
      ];

      const response = await this.anthropicClient.messages.create({
        model: modelName,
        max_tokens: 2000,
        messages: messages,
        temperature: 0.1
      });

      return (response.content[0] as { type: 'text', text: string }).text;
    }

    throw new Error(`Unsupported provider: ${provider}`);
  }

  /**
   * Generate enhanced solution with alternative approach for comparison
   */
  private async generateEnhancedSolution(
    problemInfo: any,
    language: string,
    provider: "openai" | "gemini" | "anthropic",
    signal: AbortSignal
  ): Promise<string> {
    const config = configHelper.loadConfig();
    const modelName = this.getModelForProvider(provider);

    // Check the question type and handle accordingly
    const isCodingProblem = problemInfo.is_coding_problem !== false;
    const isMultipleChoice = problemInfo.is_multiple_choice === true;
    const questionType = problemInfo.question_type || 'general';

    // Create enhanced prompts with alternative approaches
    let promptText;

    if (questionType === 'multiple_choice' || isMultipleChoice) {
      promptText = `
Analyze this multiple choice question with a comprehensive approach:

QUESTION:
${problemInfo.problem_statement}

CHOICES:
${problemInfo.choices ? problemInfo.choices.map((choice: string, index: number) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n') : 'Choices not clearly identified'}

Provide a thorough analysis in the following format:
1. Answer: The correct choice with detailed justification
2. Your Thoughts: Step-by-step reasoning process and elimination of incorrect options
3. Additional Context: Background knowledge, common misconceptions, and learning tips related to this topic
4. Alternative Perspectives: How different approaches might lead to the same answer

Focus on educational value and comprehensive understanding rather than just the correct answer.
`;
    } else if (questionType === 'short_answer') {
      promptText = `
Provide a comprehensive analysis of this short answer question:

QUESTION:
${problemInfo.problem_statement}

Deliver a thorough response in the following format:
1. Answer: A complete and precise answer
2. Your Thoughts: Detailed reasoning and methodology behind the answer
3. Additional Context: Related concepts, applications, and broader implications
4. Alternative Approaches: Different ways to think about or solve this question

Emphasize depth of understanding and practical applications.
`;
    } else if (questionType === 'code_output') {
      promptText = `
Analyze this code output question with comprehensive detail:

QUESTION:
${problemInfo.problem_statement}

CODE SNIPPET:
${problemInfo.code_snippet || 'Code not clearly identified'}

Provide an in-depth analysis in the following format:
1. Answer: The exact output with complete explanation
2. Your Thoughts: Line-by-line code execution trace and detailed analysis
3. Additional Context: Programming concepts, language features, and best practices demonstrated
4. Learning Points: Key takeaways and common pitfalls to avoid

Focus on teaching programming concepts through this specific example.
`;
    } else if (isCodingProblem || questionType === 'coding_problem') {
      promptText = `
Provide an alternative solution approach for this coding problem:

PROBLEM STATEMENT:
${problemInfo.problem_statement}

CONSTRAINTS:
${problemInfo.constraints || "No specific constraints provided."}

EXAMPLE INPUT:
${problemInfo.example_input || "No example input provided."}

EXAMPLE OUTPUT:
${problemInfo.example_output || "No example output provided."}

LANGUAGE: ${language}

Provide an alternative solution approach in the following format:
1. Code: A different implementation approach (if multiple solutions exist)
2. Your Thoughts: Alternative problem-solving strategies and trade-offs
3. Time complexity: Detailed analysis with comparison to other approaches
4. Space complexity: Detailed analysis with optimization considerations
5. Alternative Approaches: Brief discussion of other possible solutions

Focus on providing educational value through alternative perspectives and approaches.
`;
    } else {
      promptText = `
Provide a comprehensive analysis of this question:

QUESTION/PROBLEM:
${problemInfo.problem_statement}

Deliver a thorough response in the following format:
1. Answer: A comprehensive and well-reasoned response
2. Your Thoughts: Multiple perspectives and analytical approaches
3. Additional Context: Broader implications and related concepts
4. Further Exploration: Related questions and areas for deeper study

Emphasize critical thinking and comprehensive understanding.
`;
    }

    // Use the same provider logic but with enhanced prompts
    if (provider === "openai") {
      if (!this.openaiClient) {
        throw new Error("OpenAI API key not configured. Please check your settings.");
      }

      const solutionResponse = await this.openaiClient.chat.completions.create({
        model: modelName,
        messages: [
          { role: "system", content: "You are an expert educational assistant that provides comprehensive, detailed analysis with multiple perspectives. Focus on teaching and deep understanding rather than just providing answers." },
          { role: "user", content: promptText }
        ],
        max_tokens: 4000,
        temperature: 0.3 // Slightly higher temperature for more varied responses
      });

      return solutionResponse.choices[0].message.content || "";

    } else if (provider === "gemini") {
      if (!this.geminiApiKey) {
        throw new Error("Gemini API key not configured. Please check your settings.");
      }

      const geminiMessages = [
        {
          role: "user",
          parts: [
            {
              text: `You are an expert educational assistant that provides comprehensive, detailed analysis with multiple perspectives. Focus on teaching and deep understanding rather than just providing answers.\n\n${promptText}`
            }
          ]
        }
      ];

      const response = await axios.default.post(
        `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${this.geminiApiKey}`,
        {
          contents: geminiMessages,
          generationConfig: {
            temperature: 0.3,
            maxOutputTokens: 4000
          }
        },
        { signal }
      );

      const responseData = response.data as GeminiResponse;

      if (!responseData.candidates || responseData.candidates.length === 0) {
        throw new Error("Empty response from Gemini API");
      }

      return responseData.candidates[0].content.parts[0].text;

    } else if (provider === "anthropic") {
      if (!this.anthropicClient) {
        throw new Error("Anthropic API key not configured. Please check your settings.");
      }

      const messages = [
        {
          role: "user" as const,
          content: [
            {
              type: "text" as const,
              text: `You are an expert educational assistant that provides comprehensive, detailed analysis with multiple perspectives. Focus on teaching and deep understanding rather than just providing answers.\n\n${promptText}`
            }
          ]
        }
      ];

      const response = await this.anthropicClient.messages.create({
        model: modelName,
        max_tokens: 4000,
        messages: messages,
        temperature: 0.3
      });

      return (response.content[0] as { type: 'text', text: string }).text;
    }

    throw new Error(`Unsupported provider: ${provider}`);
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // Step 1: Extract problem info using AI Vision API with fallback
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // Check if any of the images are from webcam (have webcam_ prefix in filename)
      const isWebcamImage = screenshots.some(screenshot =>
        screenshot.path.includes('webcam_') || screenshot.path.includes('webcam_images')
      );

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: isWebcamImage ? "Analyzing webcam image..." : "Analyzing problem from screenshots...",
          progress: 20
        });
      }

      // Store webcam image flag for later use
      this.deps.setIsWebcamImage?.(isWebcamImage);

      // Use fallback mechanism for problem extraction
      const problemInfo = await this.tryWithFallback(
        async (provider) => {
          try {
            return await this.extractProblemInfo(imageDataList, language, provider, signal, isWebcamImage);
          } catch (error: any) {
            // Handle JSON parsing errors specifically
            if (error.message && error.message.includes('JSON')) {
              console.error(`JSON parsing error with ${provider}:`, error);
              throw new Error(`Failed to parse response from ${provider}. The AI response was not in valid JSON format.`);
            }
            throw error;
          }
        },
        "problem extraction"
      );



      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        });
      }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo);

      // Log the extracted question to console
      console.log("=".repeat(80));
      console.log("📝 EXTRACTED QUESTION DETAILS:");
      console.log("=".repeat(80));
      console.log("Problem Statement:", problemInfo.problem_statement);
      console.log("Question Type:", problemInfo.question_type || 'general');
      console.log("Is Multiple Choice:", problemInfo.is_multiple_choice || false);
      console.log("Is Coding Problem:", problemInfo.is_coding_problem !== false);

      if (problemInfo.choices && problemInfo.choices.length > 0) {
        console.log("Choices:");
        problemInfo.choices.forEach((choice: string, index: number) => {
          console.log(`  ${String.fromCharCode(65 + index)}. ${choice}`);
        });
      }

      if (problemInfo.constraints) {
        console.log("Constraints:", problemInfo.constraints);
      }

      if (problemInfo.example_input) {
        console.log("Example Input:", problemInfo.example_input);
      }

      if (problemInfo.example_output) {
        console.log("Example Output:", problemInfo.example_output);
      }

      if (problemInfo.code_snippet) {
        console.log("Code Snippet:");
        console.log(problemInfo.code_snippet);
      }

      console.log("=".repeat(80));

      // Send first success event
      if (mainWindow) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Generate solutions after successful extraction
        const solutionsResult = await this.generateSolutionsHelper(signal);
        if (solutionsResult.success) {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();

          // Final progress update
          mainWindow.webContents.send("processing-status", {
            message: "Solution generated successfully",
            progress: 100
          });

          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch (error: any) {
      // If the request was cancelled, don't retry
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      // Handle OpenAI API errors specifically
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if (error?.response?.status === 500) {
        return {
          success: false,
          error: "OpenAI server error. Please try again later."
        };
      }

      console.error("API Error Details:", error);
      return {
        success: false,
        error: error.message || "Failed to process screenshots. Please try again."
      };
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Creating optimal solution with detailed explanations...",
          progress: 60
        });
      }

      // Check if this is a coding problem or a general question
      const isCodingProblem = problemInfo.is_coding_problem !== false; // Default to true if not specified

      // Check if this is a webcam image (stored during extraction)
      const isWebcamImage = this.deps.getIsWebcamImage?.() || false;

      let responseContent: string;
      let dualResults: any = null;

      // Always generate responses from all available providers for comparison
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Generating comprehensive answers from all AI providers...",
          progress: 70
        });
      }

      // Use all available providers for all images (both webcam and screenshots)
      try {
        const allResults = await this.processWithAllProviders(
          async (provider) => {
            return await this.generateSolution(problemInfo, language, provider, signal);
          },
          "solution generation"
        );

        // Store all results for comparison display
        dualResults = {
          openai: allResults.openai,
          gemini: allResults.gemini,
          anthropic: allResults.anthropic,
          errors: allResults.errors,
          allProviders: true
        };

        // Use the first available response as primary
        responseContent = allResults.openai || allResults.gemini || allResults.anthropic || "No response available";
      } catch (error) {
        console.error("Multi-provider processing failed, falling back to single response:", error);
        // Fallback to single provider if multi-provider processing fails
        responseContent = await this.tryWithFallback(
          async (provider) => {
            return await this.generateSolution(problemInfo, language, provider, signal);
          },
          "solution generation"
        );
      }

      let formattedResponse;

      if (isCodingProblem) {
        // Extract parts from the response for coding problems
        const codeMatch = responseContent.match(/```(?:\w+)?\s*([\s\S]*?)```/);
        const code = codeMatch ? codeMatch[1].trim() : responseContent;

        // Extract thoughts, looking for bullet points or numbered lists
        const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)(?:Time complexity:|$)/i;
        const thoughtsMatch = responseContent.match(thoughtsRegex);
        let thoughts: string[] = [];

        if (thoughtsMatch && thoughtsMatch[1]) {
          // Extract bullet points or numbered items
          const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
          if (bulletPoints) {
            thoughts = bulletPoints.map(point =>
              point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
            ).filter(Boolean);
          } else {
            // If no bullet points found, split by newlines and filter empty lines
            thoughts = thoughtsMatch[1].split('\n')
              .map((line) => line.trim())
              .filter(Boolean);
          }
        }

        // Extract complexity information
        const timeComplexityPattern = /Time complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Space complexity|$))/i;
        const spaceComplexityPattern = /Space complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:[A-Z]|$))/i;

        let timeComplexity = "O(n) - Linear time complexity because we only iterate through the array once. Each element is processed exactly one time, and the hashmap lookups are O(1) operations.";
        let spaceComplexity = "O(n) - Linear space complexity because we store elements in the hashmap. In the worst case, we might need to store all elements before finding the solution pair.";

        const timeMatch = responseContent.match(timeComplexityPattern);
        if (timeMatch && timeMatch[1]) {
          timeComplexity = timeMatch[1].trim();
          if (!timeComplexity.match(/O\([^)]+\)/i)) {
            timeComplexity = `O(n) - ${timeComplexity}`;
          } else if (!timeComplexity.includes('-') && !timeComplexity.includes('because')) {
            const notationMatch = timeComplexity.match(/O\([^)]+\)/i);
            if (notationMatch) {
              const notation = notationMatch[0];
              const rest = timeComplexity.replace(notation, '').trim();
              timeComplexity = `${notation} - ${rest}`;
            }
          }
        }

        const spaceMatch = responseContent.match(spaceComplexityPattern);
        if (spaceMatch && spaceMatch[1]) {
          spaceComplexity = spaceMatch[1].trim();
          if (!spaceComplexity.match(/O\([^)]+\)/i)) {
            spaceComplexity = `O(n) - ${spaceComplexity}`;
          } else if (!spaceComplexity.includes('-') && !spaceComplexity.includes('because')) {
            const notationMatch = spaceComplexity.match(/O\([^)]+\)/i);
            if (notationMatch) {
              const notation = notationMatch[0];
              const rest = spaceComplexity.replace(notation, '').trim();
              spaceComplexity = `${notation} - ${rest}`;
            }
          }
        }

        formattedResponse = {
          code: code,
          thoughts: thoughts.length > 0 ? thoughts : ["Solution approach based on efficiency and readability"],
          time_complexity: timeComplexity,
          space_complexity: spaceComplexity,
          is_coding_problem: true,
          // Include extracted question information
          extracted_question: {
            problem_statement: problemInfo.problem_statement,
            question_type: problemInfo.question_type || 'coding_problem',
            is_multiple_choice: problemInfo.is_multiple_choice || false,
            choices: problemInfo.choices || null,
            constraints: problemInfo.constraints || null,
            example_input: problemInfo.example_input || null,
            example_output: problemInfo.example_output || null,
            code_snippet: problemInfo.code_snippet || null
          }
        };
      } else {
        // For non-coding problems, extract the answer and thoughts
        // Extract answer section
        const answerRegex = /(?:Answer:|Response:|Solution:)([\s\S]*?)(?:(?:Thoughts:|Key Insights:|Reasoning:|Approach:)|$)/i;
        const answerMatch = responseContent.match(answerRegex);
        let answer = responseContent;

        if (answerMatch && answerMatch[1]) {
          answer = answerMatch[1].trim();
        }

        // Extract thoughts, looking for bullet points or numbered lists
        const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)$/i;
        const thoughtsMatch = responseContent.match(thoughtsRegex);
        let thoughts: string[] = [];

        if (thoughtsMatch && thoughtsMatch[1]) {
          // Extract bullet points or numbered items
          const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
          if (bulletPoints) {
            thoughts = bulletPoints.map(point =>
              point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
            ).filter(Boolean);
          } else {
            // If no bullet points found, split by newlines and filter empty lines
            thoughts = thoughtsMatch[1].split('\n')
              .map((line) => line.trim())
              .filter(Boolean);
          }
        }

        formattedResponse = {
          code: answer, // Use the code field to store the answer for display
          thoughts: thoughts.length > 0 ? thoughts : ["Key insights about the answer"],
          time_complexity: "N/A - Not a coding problem",
          space_complexity: "N/A - Not a coding problem",
          is_coding_problem: false,
          // Include extracted question information
          extracted_question: {
            problem_statement: problemInfo.problem_statement,
            question_type: problemInfo.question_type || 'general',
            is_multiple_choice: problemInfo.is_multiple_choice || false,
            choices: problemInfo.choices || null,
            constraints: problemInfo.constraints || null,
            example_input: problemInfo.example_input || null,
            example_output: problemInfo.example_output || null,
            code_snippet: problemInfo.code_snippet || null
          }
        };
      }

      // If we have dual results from multi-provider processing, include all responses
      if (dualResults) {
        const formatSingleResponse = (content: string, providerName: string) => {
          if (!content) return null;

          if (isCodingProblem) {
            const codeMatch = content.match(/```(?:\w+)?\s*([\s\S]*?)```/);
            const code = codeMatch ? codeMatch[1].trim() : content;

            const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)(?:Time complexity:|$)/i;
            const thoughtsMatch = content.match(thoughtsRegex);
            let thoughts: string[] = [];

            if (thoughtsMatch && thoughtsMatch[1]) {
              const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
              if (bulletPoints) {
                thoughts = bulletPoints.map(point =>
                  point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
                ).filter(Boolean);
              } else {
                thoughts = thoughtsMatch[1].split('\n')
                  .map((line) => line.trim())
                  .filter(Boolean);
              }
            }

            return {
              provider: providerName,
              code: code,
              thoughts: thoughts.length > 0 ? thoughts : [`${providerName} solution approach`],
              is_coding_problem: true
            };
          } else {
            const answerRegex = /(?:Answer:|Response:|Solution:)([\s\S]*?)(?:(?:Thoughts:|Key Insights:|Reasoning:|Approach:)|$)/i;
            const answerMatch = content.match(answerRegex);
            let answer = content;

            if (answerMatch && answerMatch[1]) {
              answer = answerMatch[1].trim();
            }

            const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)$/i;
            const thoughtsMatch = content.match(thoughtsRegex);
            let thoughts: string[] = [];

            if (thoughtsMatch && thoughtsMatch[1]) {
              const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
              if (bulletPoints) {
                thoughts = bulletPoints.map(point =>
                  point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
                ).filter(Boolean);
              } else {
                thoughts = thoughtsMatch[1].split('\n')
                  .map((line) => line.trim())
                  .filter(Boolean);
              }
            }

            return {
              provider: providerName,
              code: answer,
              thoughts: thoughts.length > 0 ? thoughts : [`${providerName} insights about the answer`],
              is_coding_problem: false
            };
          }
        };

        const dualResponses = [];

        // Handle the new enhanced dual results format
        if (dualResults.primary) {
          const primaryResponse = formatSingleResponse(dualResults.primary, `${dualResults.provider} - Standard Analysis`);
          if (primaryResponse) dualResponses.push(primaryResponse);
        }
        if (dualResults.enhanced) {
          const enhancedResponse = formatSingleResponse(dualResults.enhanced, `${dualResults.provider} - Enhanced Analysis`);
          if (enhancedResponse) dualResponses.push(enhancedResponse);
        }

        // Support for all three providers
        if (dualResults.openai) {
          const openaiResponse = formatSingleResponse(dualResults.openai, "OpenAI GPT-4o");
          if (openaiResponse) dualResponses.push(openaiResponse);
        }
        if (dualResults.gemini) {
          const geminiResponse = formatSingleResponse(dualResults.gemini, "Google Gemini");
          if (geminiResponse) dualResponses.push(geminiResponse);
        }
        if (dualResults.anthropic) {
          const anthropicResponse = formatSingleResponse(dualResults.anthropic, "Claude Sonnet");
          if (anthropicResponse) dualResponses.push(anthropicResponse);
        }

        // Add dual responses to the main response
        formattedResponse.dual_responses = dualResponses;
        formattedResponse.is_dual_response = true;
        formattedResponse.provider_errors = dualResults.errors;

        // Create comparison note based on available providers
        if (dualResults.allProviders) {
          const availableProviders = [];
          if (dualResults.openai) availableProviders.push("OpenAI");
          if (dualResults.gemini) availableProviders.push("Gemini");
          if (dualResults.anthropic) availableProviders.push("Anthropic");
          formattedResponse.comparison_note = `Showing comparative analysis from ${availableProviders.join(", ")} for comprehensive insights`;
        } else {
          formattedResponse.comparison_note = dualResults.provider ?
            `Showing two different analytical approaches using ${dualResults.provider}` :
            "Showing comparative analysis from multiple perspectives";
        }
      }

      return { success: true, data: formattedResponse };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      }

      console.error("Solution generation error:", error);
      return { success: false, error: error.message || "Failed to generate solution" };
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        });
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      let debugContent;

      if (config.apiProvider === "openai") {
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        const messages = [
          {
            role: "system" as const,
            content: `You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

Your response MUST follow this exact structure with these section headers (use ### for headers):
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution. Here are screenshots of my code, the errors or test cases. Please provide a detailed analysis with:
1. What issues you found in my code
2. Specific improvements and corrections
3. Any optimizations that would make the solution better
4. A clear explanation of the changes needed`
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        if (mainWindow) {
          mainWindow.webContents.send("processing-status", {
            message: "Analyzing code and generating debug feedback...",
            progress: 60
          });
        }

        const debugResponse = await this.openaiClient.chat.completions.create({
          model: config.debuggingModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });

        debugContent = debugResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini...",
              progress: 60
            });
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.debuggingModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          debugContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for debugging:", error);
          return {
            success: false,
            error: "Failed to process debug request with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: debugPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Claude...",
              progress: 60
            });
          }

          const response = await this.anthropicClient.messages.create({
            model: config.debuggingModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          debugContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for debugging:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process debug request with Anthropic API. Please check your API key or try again later."
          };
        }
      }


      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Debug analysis complete",
          progress: 100
        });
      }

      let extractedCode = "// Debug mode - see analysis below";
      const codeMatch = debugContent.match(/```(?:[a-zA-Z]+)?([\s\S]*?)```/);
      if (codeMatch && codeMatch[1]) {
        extractedCode = codeMatch[1].trim();
      }

      let formattedDebugContent = debugContent;

      if (!debugContent.includes('# ') && !debugContent.includes('## ')) {
        formattedDebugContent = debugContent
          .replace(/issues identified|problems found|bugs found/i, '## Issues Identified')
          .replace(/code improvements|improvements|suggested changes/i, '## Code Improvements')
          .replace(/optimizations|performance improvements/i, '## Optimizations')
          .replace(/explanation|detailed analysis/i, '## Explanation');
      }

      const bulletPoints = formattedDebugContent.match(/(?:^|\n)[ ]*(?:[-*•]|\d+\.)[ ]+([^\n]+)/g);
      const thoughts = bulletPoints
        ? bulletPoints.map(point => point.replace(/^[ ]*(?:[-*•]|\d+\.)[ ]+/, '').trim()).slice(0, 5)
        : ["Debug analysis based on your screenshots"];

      const response = {
        code: extractedCode,
        debug_analysis: formattedDebugContent,
        thoughts: thoughts,
        time_complexity: "N/A - Debug mode",
        space_complexity: "N/A - Debug mode"
      };

      return { success: true, data: response };
    } catch (error: any) {
      console.error("Debug processing error:", error);
      return { success: false, error: error.message || "Failed to process debug request" };
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    this.deps.setHasDebugged(false)

    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }
}
