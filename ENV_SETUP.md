# Environment Variables Setup

## Quick Setup

1. **Copy the example file**:
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file** with your actual API keys:
   ```bash
   nano .env
   # or
   code .env
   ```

3. **Get API keys** from one or more providers:

### OpenAI API Key
- Go to: https://platform.openai.com/api-keys
- Create a new API key
- Format: `sk-...` (starts with "sk-")
- Add to `.env`: `OPENAI_API_KEY=sk-your-actual-key-here`

### Google Gemini API Key
- Go to: https://aistudio.google.com/app/apikey
- Create a new API key
- Add to `.env`: `GEMINI_API_KEY=your-actual-key-here`

### Anthropic Claude API Key
- Go to: https://console.anthropic.com/
- Create a new API key
- Format: `sk-ant-...` (starts with "sk-ant-")
- Add to `.env`: `ANTHROPIC_API_KEY=sk-ant-your-actual-key-here`

## Important Notes

- **You need at least ONE valid API key** for the application to work
- The `.env` file is now preserved during builds (fixed stealth-run scripts)
- The `.env` file is in `.gitignore` so your keys won't be committed to git
- Use `.env.example` as a template - it won't be deleted

## Troubleshooting

If your `.env` file keeps getting deleted:
1. Check if you're running `cleanup-for-github.sh` or `cleanup-for-github.bat`
2. Make sure you're using the updated stealth-run scripts
3. The file should persist now with the fixes applied

## Testing

After setting up your API keys:
1. Restart the application: `npm run dev`
2. Navigate to the webcam page
3. The buttons should no longer be grayed out
